import requests
import urllib3
import json

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_STRUCTURE = "ZMXAPIMEASUREMENT"  # ✅ No trailing space
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

where_clause = (
    f'siteid="UTIL.GM" and '
    f'zmeasuredate="2025-06-04T12:15:15+00:00"'
)

params = {
    "oslc.where": where_clause,
    "oslc.select": "*",
    "oslc.pageSize": "1",
    "lean": "1"
}


headers = {
    "Accept": "application/json",
    "apikey": API_KEY
}

url = f"{BASE_URL}/oslc/os/{OBJECT_STRUCTURE}"

resp = requests.get(url, headers=headers, params=params, verify=False)
resp.raise_for_status()

data = resp.json()
with open("response.json", "w") as f:
    json.dump(data, f, indent=2)

print("✅ Data saved to response.json")
