import requests
import urllib3
import sqlite3
import time

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configuration
BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_STRUCTURE = "ZMXAPIDOCLINKS"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
DB_PATH = "data.db"
BATCH_SIZE = 50  # Lowered to avoid too long WHERE clause

# Step 1: Get (z_ex_measurementid, zinspector) pairs from SQLite
conn = sqlite3.connect(DB_PATH)
cur = conn.cursor()
cur.execute("""
    SELECT z_ex_measurementid, zinspector 
    FROM daily_check 
    WHERE z_ex_measurementid IS NOT NULL AND zinspector IS NOT NULL
""")
records = [(str(row[0]), row[1]) for row in cur.fetchall()]
conn.close()

# Step 2: Split into batches
def chunks(lst, size):
    for i in range(0, len(lst), size):
        yield lst[i:i + size]

headers = {
    "Accept": "application/json",
    "apikey": API_KEY
}

all_results = []

from collections import defaultdict

# Step 2: Group ownerids by inspector
grouped = defaultdict(list)
for ownerid, inspector in records:
    grouped[inspector].append(ownerid)

# Step 3: Loop over each inspector group
for inspector, id_list in grouped.items():
    for batch_ids in chunks(id_list, BATCH_SIZE):
        where_clause = f'changeby="{inspector}" and ownerid in [{",".join(batch_ids)}]'
        params = {
            "oslc.where": where_clause,
            "lean": "1",
            "oslc.select": "*",
        }

        try:
            response = requests.get(
                f"{BASE_URL}/oslc/os/{OBJECT_STRUCTURE}",
                headers=headers,
                params=params,
                verify=False
            )
            response.raise_for_status()
            batch_data = response.json().get("member", [])
            all_results.extend(batch_data)
            print(f"✅ {len(batch_data)} items for {inspector}, ownerid batch {batch_ids[0]}–{batch_ids[-1]}")
            time.sleep(0.3)
        except requests.RequestException as e:
            print(f"❌ Failed for {inspector}, batch {batch_ids[0]}–{batch_ids[-1]}: {e}")


# Step 4: Save to data.db
conn = sqlite3.connect(DB_PATH)
cur = conn.cursor()
cur.execute("CREATE TABLE IF NOT EXISTS doc_links (ownerid TEXT, changeby TEXT, weburl TEXT, description TEXT)")
cur.executemany("INSERT INTO doc_links VALUES (?, ?, ?, ?)", [
    (item['ownerid'], item['changeby'], item['weburl'], item['description']) 
    for item in all_results
])
conn.commit()
conn.close()
