import sqlite3
import pandas as pd

# Path to your SQLite database
db_path = 'data.db'

# Connect to the database
conn = sqlite3.connect(db_path)

# SQL query to fetch the table
query = "SELECT * FROM WO_raw_data"

# Read the table into a DataFrame
df = pd.read_sql_query(query, conn)

# Close the database connection
conn.close()

# Save to Excel file
excel_path = 'WO_raw_data_export.xlsx'
df.to_excel(excel_path, index=False)

print(f"Exported 'WO_raw_data' to '{excel_path}' successfully.")


import requests

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "MXAPIAPIKEY"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

params = {
    "lean": "1",
    "oslc.select": "*",
    "oslc.pageSize": "100"
}

response = requests.get(f"{BASE_URL}/oslc/os/{OBJECT_NAME}", headers=headers, params=params, verify=False)

print(response.json())


import requests
import pandas as pd

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "ZMXAPIKEYTOKEN"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

params = {
    "lean": "1",
    "oslc.select": "*"
}

response = requests.get(
    f"{BASE_URL}/oslc/os/{OBJECT_NAME}", 
    headers=headers, 
    params=params, 
    verify=False
)

data = response.json()

# Extract the list of records
records = data.get("member", [])

# Prepare a list of dicts with the selected fields
rows = []
for r in records:
    rows.append({
        "_rowstamp": r.get("_rowstamp"),
        "creationdate": r.get("creationdate"),
        "apikey": r.get("apikey"),
        "apikeytokenid": r.get("apikeytokenid"),
        "expiration": r.get("expiration"),
        "href": r.get("href"),
        "userid": r.get("userid")
    })

# Convert to DataFrame
df = pd.DataFrame(rows)

# Save to Excel
df.to_excel("ZMXAPIKEYTOKEN.xlsx", index=False)

print("✅ Saved API key info (including userid) to ZMXAPIKEYTOKEN.xlsx")


import requests
import json

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "ZLGAPIUSERINFO"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

params = {
    "lean": "1",
    "oslc.select": "*",
    "oslc.pageSize": "10"
}

# Prepare the request to see the full URL
req = requests.Request(
    method="GET",
    url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
    headers=headers,
    params=params
)
prepared = req.prepare()

# Print the full URL
print("Full URL:")
print(prepared.url)

# Send the request
session = requests.Session()
response = session.send(prepared, verify=False)

# Print response info and JSON nicely
print("\nResponse status code:", response.status_code)
data = response.json()
print(json.dumps(data, indent=2))

import requests

url = "https://ems-lgensol.singlex.com/maximo/oslc/os/mxlabor"  # replace with actual API URL
headers = {
    "apikey": "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla",
    "Accept": "application/json"
}

params = {
    "lean": 1,
    "oslc.select": "*",
    "oslc.pageSize": 10
}

req = requests.Request(
    method="GET",
    url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
    headers=headers,
    params=params
)
prepared = req.prepare()

# Print the full URL
print("Full URL:")
print(prepared.url)

data = response.json()
print(data)
