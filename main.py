import time
import datetime
import subprocess

def run_scripts():
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"\n🕒 Running scripts at {now}")

    try:
        print("🚀 Running get_WO_data.py...")
        subprocess.run(["python", "get_WO_data.py"], check=True)
        print("✅ get_WO_data.py finished.")
    except subprocess.CalledProcessError as e:
        print(f"❌ get_WO_data.py failed: {e}")
        return

    try:
        print("📧 Running send_email.py...")
        subprocess.run(["python", "send_email.py"], check=True)
        print("✅ send_email.py finished.")
    except subprocess.CalledProcessError as e:
        print(f"❌ send_email.py failed: {e}")

def wait_until(target_hour):
    """Wait until the next target_hour (6 or 18)."""
    now = datetime.datetime.now()
    target = now.replace(hour=target_hour, minute=0, second=0, microsecond=0)
    if now >= target:
        target += datetime.timedelta(days=1)
    wait_seconds = (target - now).total_seconds()
    print(f"⏳ Waiting until {target.strftime('%Y-%m-%d %H:%M:%S')} ({int(wait_seconds)}s)")
    time.sleep(wait_seconds)


# if __name__ == "__main__":
#     while True:
#         now = datetime.datetime.now()

#         # Determine next run: either 6am or 6pm
#         if now.hour < 6:
#             next_run_hour = 6
#         elif now.hour < 18:
#             next_run_hour = 18
#         else:
#             next_run_hour = 6  # next day's 6am

#         wait_until(next_run_hour)
#         run_scripts()

if __name__ == "__main__":
    run_scripts()
