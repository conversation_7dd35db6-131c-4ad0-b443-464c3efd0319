import requests
import json
import sqlite3
import urllib3
import os
import pytz
import pandas as pd
from datetime import datetime, timedelta
import subprocess

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def get_shift_start_end(now_local=None):
    """Determine shift start and end datetime in UTC based on current local time (Eastern)."""
    eastern = pytz.timezone("America/New_York")
    
    if now_local is None:
        now_local = datetime.now(eastern)-timedelta(hours=12) # minus 12 hours to get previous shift
    else:
        now_local = pd.to_datetime(now_local).tz_localize(eastern)

    if 6 <= now_local.hour < 18:
        shift_start = now_local.replace(hour=6, minute=0, second=0, microsecond=0)
        shift_end = now_local.replace(hour=18, minute=0, second=0, microsecond=0)
    else:
        if now_local.hour < 6:
            shift_start = now_local.replace(hour=18, minute=0, second=0, microsecond=0) - timedelta(days=1)
        else:
            shift_start = now_local.replace(hour=18, minute=0, second=0, microsecond=0)
        shift_end = shift_start + timedelta(hours=12)

    # Convert to UTC and ISO format
    shift_start_utc = shift_start.astimezone(pytz.utc).isoformat()
    shift_end_utc = shift_end.astimezone(pytz.utc).isoformat()

    return shift_start_utc, shift_end_utc

# 👇 Use the function to get dynamic WHERE clause
start_utc, end_utc = get_shift_start_end()

# --- Configuration ---
BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_STRUCTURE = "ZMXAPIMEASUREMENT"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
DB_PATH = "data.db"
TABLE_NAME = "daily_check"

# --- OSLC WHERE clauses ---
where_clause1 = (
    f'siteid="UTIL.GM" and '
    f'zschedstart>="{start_utc}" and '
    f'zschedstart<"{end_utc}"'
)

where_clause2 = (
    f'siteid="UTIL.GM" and '
    f'zmeasuredate>="{start_utc}" and '
    f'zmeasuredate<"{end_utc}"'
)

# where_clause2 = (
#     f'siteid="UTIL.GM" and '
#     f'zmeasuredate="2025-06-04T12:15:15+00:00"'
# )

params_base = {
    "oslc.select": "zobservation, zownergroup, zmeasuredate, zassetnum, zresult, zpointnum, zancestor, zdescription, z_ex_measurementid, zinspector, zschedstart, zschedfinish, zremarks",
    "lean": "1"
}

headers = {
    "Accept": "application/json",
    "apikey": API_KEY
}

def fetch_data(where_clause):
    params = params_base.copy()
    params["oslc.where"] = where_clause
    try:
        response = requests.get(
            f"{BASE_URL}/oslc/os/{OBJECT_STRUCTURE}",
            headers=headers,
            params=params,
            verify=False
        )
        response.raise_for_status()
        return response.json().get("member", [])
    except requests.RequestException as e:
        print(f"❌ Request failed for clause: {where_clause}\n{e}")
        return []

# Step 1: Fetch data from both clauses
records1 = fetch_data(where_clause1)
records2 = fetch_data(where_clause2)

print(f"🔢 Retrieved {len(records1)} from where_clause1, {len(records2)} from where_clause2")

# Step 2: Merge and deduplicate by _rowstamp
all_records = {r["_rowstamp"]: r for r in (records1 + records2)}  # dict removes duplicates
deduped_records = list(all_records.values())
print(f"✅ Total unique records: {len(deduped_records)}")

# Step 3: Set up DB and ensure table exists
if not os.path.exists(DB_PATH):
    open(DB_PATH, "w").close()

conn = sqlite3.connect(DB_PATH)
cur = conn.cursor()

# Remove existing table if it exists
cur.execute(f"DROP TABLE IF EXISTS {TABLE_NAME}")

cur.execute(f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        z_ex_measurementid INTEGER PRIMARY KEY,
        zobservation TEXT,
        zownergroup TEXT,
        zmeasuredate TEXT,
        zassetnum TEXT,
        zresult TEXT,
        zpointnum INTEGER,
        zancestor TEXT,
        zdescription TEXT,
        zschedstart TEXT,
        zschedfinish TEXT,
        zinspector TEXT,
        zremarks TEXT
    )
""")

# Step 4: Insert only new records
inserted = 0
for r in deduped_records:
    try:
        cur.execute(f"""
            INSERT OR IGNORE INTO {TABLE_NAME} (
                z_ex_measurementid, zobservation, zownergroup, zmeasuredate,
                zassetnum, zresult, zpointnum, zancestor,
                zdescription, zinspector, zschedstart, zschedfinish, zremarks
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            r.get("z_ex_measurementid"),
            r.get("zobservation"),
            r.get("zownergroup"),
            r.get("zmeasuredate"),
            r.get("zassetnum"),
            r.get("zresult"),
            r.get("zpointnum"),
            r.get("zancestor"),
            r.get("zdescription"),
            r.get("zinspector"),
            r.get("zschedstart"),
            r.get("zschedfinish"),
            r.get("zremarks")
        ))
        if cur.rowcount > 0:
            inserted += 1
    except sqlite3.IntegrityError:
        continue

conn.commit()
conn.close()

print(f"✅ Inserted {inserted} new records into '{TABLE_NAME}' in '{DB_PATH}'.")

# ================= Loop each doclinks ==================

# run get_doc_link.py
print("\n")
print("="*60)
print("Running get_doc_link.py...")
get_doclinks_script = "get_doc_link.py"
subprocess.run(["python", get_doclinks_script])

# Step 1: Connect to the database
conn = sqlite3.connect(DB_PATH)
cur = conn.cursor()

# Step 2: Add `weburl` column if it doesn't exist
cur.execute(f"""
    PRAGMA table_info({TABLE_NAME})
""")
columns = [col[1] for col in cur.fetchall()]
if "weburl" not in columns:
    print("🛠️ Adding 'weburl' column to daily_check...")
    cur.execute(f"ALTER TABLE {TABLE_NAME} ADD COLUMN weburl TEXT")

# Step 3: Update daily_check.weburl using join with doc_links
print("🔗 Updating 'weburl' values using join with doc_links...")
cur.execute(f"""
    UPDATE {TABLE_NAME}
    SET weburl = (
        SELECT doc_links.weburl
        FROM doc_links
        WHERE doc_links.ownerid = daily_check.z_ex_measurementid
          AND doc_links.changeby = daily_check.zinspector
    )
    WHERE EXISTS (
        SELECT 1
        FROM doc_links
        WHERE doc_links.ownerid = daily_check.z_ex_measurementid
          AND doc_links.changeby = daily_check.zinspector
    )
""")

# Step 4: Commit and close
conn.commit()
conn.close()
print("✅ Finished updating 'weburl' in daily_check.")
print("="*60)

# ========== JOIN Labor name and Daily Check =============
# Connect to the SQLite database
conn = sqlite3.connect(DB_PATH)
cur = conn.cursor()

# --- Step 1: Ensure displayname column exists ---
cur.execute("PRAGMA table_info(daily_check)")
columns = [col[1] for col in cur.fetchall()]
if "displayname" not in columns:
    print("🛠️ Adding 'displayname' column to daily_check...")
    cur.execute("ALTER TABLE daily_check ADD COLUMN displayname TEXT")

# --- Step 2: Update displayname from labor_users.personid ---
update_displayname_query = """
    UPDATE daily_check
    SET displayname = (
        SELECT labor_users.displayname
        FROM labor_users
        WHERE labor_users.personid = daily_check.zinspector
    )
    WHERE EXISTS (
        SELECT 1
        FROM labor_users
        WHERE labor_users.personid = daily_check.zinspector
    )
"""
cur.execute(update_displayname_query)

# --- Step 3: Replace zownergroup values ---
group_mapping = {
    'GM.UT.B': 'Boiler',
    'GM.UT.H': 'HVAC',
    'GM.UT.M': 'Mechanical',
    'GM.UT.O': 'Operator',
    'GM.UT.E': 'Electrical'
}

for original, replacement in group_mapping.items():
    cur.execute(
        "UPDATE daily_check SET zownergroup = ? WHERE zownergroup = ?",
        (replacement, original)
    )

# --- Commit and close ---
conn.commit()
conn.close()
print("✅ Updated 'displayname' and replaced 'zownergroup' values in 'daily_check'.")