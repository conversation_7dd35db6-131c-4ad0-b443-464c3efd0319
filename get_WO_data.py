import requests
import os
import urllib3
import sqlite3
import pandas as pd
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def load_last_refresh_date():
    filename = "last_refresh_date.txt"
    if os.path.exists(filename):
        with open(filename, "r") as f:
            return f.read().strip()

def save_last_refresh_date(iso_str):
    with open("last_refresh_date.txt", "w") as f:
        f.write(iso_str)

LAST_REFRESH_DATE = load_last_refresh_date()
print(f"LAST REFRESH DATE in UTC: {LAST_REFRESH_DATE}\n")

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_STRUCTURE = "oslcwodetail"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
DB_PATH = os.path.join("data.db")

# Fixed WHERE clause with correct spacing
where_clause = (
    'spi_wm:siteid="UTIL.GM" and '
    'spi_wm:ownergroup!="GM.UT.U" and '
    'spi_wm:istask=0 and '
    'spi:status!="DRAFT" and '
    'spi:status!="INPRG" and '
    'spi:status!="OPCOMP" and '
    'spi:status!="CAN" and '
    f'dcterms:modified>"{LAST_REFRESH_DATE}"'
)

params = {
    "oslc.where": where_clause,
    "oslc.select": "wostatus",
    "lean": "1"
}


headers = {
    "Accept": "application/json",
    "apikey": API_KEY
}

url = f"{BASE_URL}/oslc/os/{OBJECT_STRUCTURE}"

print(f"🔍 Querying work orders detail with WHERE clause: {where_clause}")
resp = requests.get(url, headers=headers, params=params, verify=False)

# Connect to DB
conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

# Create fresh table
# cursor.execute("DROP TABLE IF EXISTS WO_COMP_status")
# cursor.execute("""
# CREATE TABLE WO_COMP_status (
#     wonum TEXT,
#     changeby TEXT,
#     changedate TEXT,
#     status TEXT
# )
# """)

# Extract and filter records
rows = []
for member in resp.json().get("member", []):
    wonum = member.get("wonum") or member.get("spi_wm:wonum")
    for ws in member.get("wostatus", []):
        if ws.get("status") == "COMP":
            rows.append((
                ws.get("parent") or wonum,
                ws.get("changeby"),
                ws.get("changedate"),
                ws.get("status"),
            ))

# Insert all COMP status records
cursor.executemany("""
INSERT INTO WO_COMP_status (wonum, changeby, changedate, status)
VALUES (?, ?, ?, ?)
""", rows)

# Keep only the latest status per wonum
cursor.execute("""
SELECT o1.wonum, o1.changeby, o1.changedate, o1.status
FROM WO_COMP_status o1
JOIN (
    SELECT wonum, MAX(changedate) AS latest_changedate
    FROM WO_COMP_status
    GROUP BY wonum
) o2 ON o1.wonum = o2.wonum AND o1.changedate = o2.latest_changedate
""")
filtered_rows = cursor.fetchall()

# Overwrite the table with filtered records
cursor.execute("DELETE FROM WO_COMP_status")
cursor.executemany("""
INSERT INTO WO_COMP_status (wonum, changeby, changedate, status)
VALUES (?, ?, ?, ?)
""", filtered_rows)

# Save and close
conn.commit()
conn.close()

print(f"\n✅ Saved {len(filtered_rows)} latest COMP records to {DB_PATH} in table WO_COMP_status.")


# =============== Get work order info ===================
BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_STRUCTURE = "MXWO"
TABLE_NAME = "MXWO_WorkOrder"

where_clause = (
    'woclass="WORKORDER" and '
    'istask=0 and '
    'worktype!="DM" and '
    'zinfowoflag=0 and '
    'status!="DRAFT" and '
    'status!="INPRG" and '
    'status!="OPCOMP" and '
    'status!="CAN" and '
    'siteid="UTIL.GM" and '
    'ownergroup!="GM.UT.U" and '
    f'changedate>"{LAST_REFRESH_DATE}"'
)

params = {
    "oslc.where": where_clause,
    "oslc.select": "wonum,status,description,ownergroup,schedstart,schedfinish,actstart,actfinish,jpnum,assetnum,workorderid",
    "lean": "1"
}

try:
    response = requests.get(
        f"{BASE_URL}/oslc/os/{OBJECT_STRUCTURE}",
        headers=headers,
        params=params,
        verify=False
    )
    response.raise_for_status()
except requests.RequestException as e:
    print(f"❌ Request failed: {e}")
    exit(1)

print("✅ Request successful.")
members = response.json().get("member", [])

print(f"📦 Received {len(members)} work order records from Maximo.")

# =============== Save to SQLite ===================
print("💾 Preparing to save records to local SQLite DB...")

conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

# # remove table data
# cursor.execute(f"DROP TABLE IF EXISTS {TABLE_NAME}")

# Create the table if it doesn't exist
print("📄 Creating table (if not exists)...")
cursor.execute(f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        wonum TEXT PRIMARY KEY,
        status TEXT,
        description TEXT,
        ownergroup TEXT,
        schedstart TEXT,
        schedfinish TEXT,
        actstart TEXT,
        actfinish TEXT,
        jpnum TEXT,
        assetnum TEXT,
        workorderid TEXT
    )
""")

print("📝 Inserting records...")
for i, row in enumerate(members, start=1):
    cursor.execute(f"""
        INSERT OR IGNORE INTO {TABLE_NAME} (
            wonum, status, description, ownergroup, schedstart, schedfinish,
            actstart, actfinish, jpnum, assetnum, workorderid
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, (
        row.get("wonum"),
        row.get("status"),
        row.get("description"),
        row.get("ownergroup"),
        row.get("schedstart"),
        row.get("schedfinish"),
        row.get("actstart"),
        row.get("actfinish"),
        row.get("jpnum"),
        row.get("assetnum"),
        row.get("workorderid")
    ))
    if i % 100 == 0:
        print(f"  ➕ Inserted {i} records...")

conn.commit()
conn.close()

# update last refresh date text file
new_refresh_date = datetime.now(timezone.utc).replace(microsecond=0).isoformat()
save_last_refresh_date(new_refresh_date)
print(f"🕒 Updated last refresh date to: {new_refresh_date}")

# # =============== INNER JOIN assetnum to equipment description ===================
# Connect to the database
conn = sqlite3.connect("Data.db")
cursor = conn.cursor()

# Step 1: JOIN + SELECT from source tables
cursor.execute("""
SELECT 
    s.wonum,
    s.changeby,
    s.changedate,
    s.status,
    w.status AS wo_status,
    w.description,
    w.ownergroup,
    w.schedstart,
    w.schedfinish,
    w.actstart,
    w.actfinish,
    w.jpnum,
    w.assetnum,
    w.workorderid
FROM 
    WO_COMP_status s
INNER JOIN 
    MXWO_WorkOrder w
ON 
    s.wonum = w.wonum
""")
rows = cursor.fetchall()  # ✅ Missing in your version

# Step 2: Convert date fields to US/Eastern
def to_est(dt_string):
    if not dt_string:
        return None
    try:
        dt = datetime.fromisoformat(dt_string.replace("Z", "+00:00"))
        return dt.astimezone(ZoneInfo("US/Eastern")).isoformat()
    except Exception:
        return dt_string

department_mapping = {
    'GM.UT.B': 'Boiler',
    'GM.UT.H': 'HVAC',
    'GM.UT.M': 'Mechanical',
    'GM.UT.O': 'Operator',
    'GM.UT.U': 'Utility',
    'GM.UT.E': 'Electrical'
}

converted_rows = []
for row in rows:
    (
        wonum, changeby, changedate, status,
        wo_status, description, ownergroup,
        schedstart, schedfinish, actstart, actfinish,
        jpnum, assetnum, workorderid
    ) = row

    # Map ownergroup to department name (fallback to original if not found)
    department = department_mapping.get(ownergroup, ownergroup)

    converted_rows.append((
        wonum,
        changeby,
        to_est(changedate),
        status,
        wo_status,
        description,
        department,  # mapped value
        to_est(schedstart),
        to_est(schedfinish),
        to_est(actstart),
        to_est(actfinish),
        jpnum,
        assetnum,
        workorderid
    ))

# Step 3: Create final table and insert converted data
cursor.execute("DROP TABLE IF EXISTS WO_raw_data")

cursor.execute("""
CREATE TABLE WO_raw_data (
    wonum TEXT,
    changeby TEXT,
    changedate TEXT,
    status TEXT,
    wo_status TEXT,
    description TEXT,
    ownergroup TEXT,
    schedstart TEXT,
    schedfinish TEXT,
    actstart TEXT,
    actfinish TEXT,
    jpnum TEXT,
    assetnum TEXT,
    workorderid TEXT
)
""")

cursor.executemany("""
INSERT INTO WO_raw_data VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
""", converted_rows)

conn.commit()
conn.close()

# =============== Get equipment info ===================
BASE_URL_ASSET = "https://ems-lgensol.singlex.com/maximo/oslc/os/MXASSET"
HEADERS = {"Accept": "application/json"}

params = {
    "oslc.where": 'siteid="UTIL.GM" and status="ACTIVE"',
    "oslc.select": "assetnum,description",
    "apikey": API_KEY
}

# API Call (only once)
print("🔍 Requesting asset data from Maximo...")
try:
    response = requests.get(BASE_URL_ASSET, headers=HEADERS, params=params, verify=False)
    response.raise_for_status()
    data = response.json()

    # Prepare DB
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute("""
        CREATE TABLE IF NOT EXISTS equipment_master (
            assetnum TEXT PRIMARY KEY,
            description TEXT
        )
    """)
    cursor.execute("DELETE FROM equipment_master")

    # Insert results into DB
    assets = data.get("rdfs:member", [])
    for asset in assets:
        assetnum = asset.get("spi:assetnum", "N/A")
        description = asset.get("spi:description", "N/A")
        cursor.execute(
            "INSERT OR REPLACE INTO equipment_master (assetnum, description) VALUES (?, ?)",
            (assetnum, description)
        )

    # Finalize
    conn.commit()
    conn.close()
    print(f"✅ {len(assets)} ACTIVE assets from UTIL.GM saved to {DB_PATH}.")

except requests.exceptions.RequestException as e:
    print(f"❌ Error fetching asset data: {e}")
except Exception as e:
    print(f"❌ Error processing asset data: {e}")

# ============== Join Equipment ID to Description ==================
conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

cursor.execute("""
    SELECT 
        r.wonum,
        r.changeby,
        r.changedate,
        r.status,
        r.wo_status,
        r.description,
        r.ownergroup,
        r.schedstart,
        r.schedfinish,
        r.actstart,
        r.actfinish,
        r.jpnum,
        r.assetnum,
        r.workorderid,
        e.description AS equipment_description
    FROM 
        WO_raw_data r
    LEFT JOIN 
        equipment_master e
    ON 
        r.assetnum = e.assetnum
""")
rows = cursor.fetchall()

cursor.execute("DROP TABLE IF EXISTS WO_raw_data")
cursor.execute("""
    CREATE TABLE WO_raw_data (
        wonum TEXT,
        changeby TEXT,
        changedate TEXT,
        status TEXT,
        wo_status TEXT,
        description TEXT,
        ownergroup TEXT,
        schedstart TEXT,
        schedfinish TEXT,
        actstart TEXT,
        actfinish TEXT,
        jpnum TEXT,
        assetnum TEXT,
        workorderid TEXT,
        equipment_description TEXT,
        main_equipment TEXT
    )
""")

def extract_main_equipment(equipment_desc):
    if not equipment_desc:
        return None
    if "-" in equipment_desc:
        return equipment_desc.split("-", 1)[0].strip()
    return equipment_desc.strip()

new_rows = []
for row in rows:
    # row is a tuple with 15 elements, last one is equipment_description
    *rest, equipment_desc = row
    main_equipment = extract_main_equipment(equipment_desc)
    new_rows.append((*rest, equipment_desc, main_equipment))

cursor.executemany("""
    INSERT INTO WO_raw_data VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
""", new_rows)

conn.commit()
conn.close()

# --- Connect to your database ---
conn = sqlite3.connect(DB_PATH)
cur = conn.cursor()

# --- Step 1: Add new column 'name' if it does not exist ---
try:
    cur.execute("ALTER TABLE WO_raw_data ADD COLUMN name TEXT;")
except sqlite3.OperationalError:
    # Column already exists
    pass

# --- Step 2: Update WO_raw_data.name with labor_users.displayname ---
cur.execute("""
    UPDATE WO_raw_data
    SET name = (
        SELECT displayname
        FROM labor_users
        WHERE labor_users.personid = WO_raw_data.changeby
    )
""")

# --- Commit and close ---
conn.commit()
conn.close()

print("✅ WO_raw_data updated with 'name' column from labor_users.displayname.")