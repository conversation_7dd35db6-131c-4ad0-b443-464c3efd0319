from datetime import datetime, timedelta
import win32com.client as win32
import sqlite3
import pandas as pd

def get_shift_start_end(shift_start_time):
    if pd.isna(shift_start_time):
        return pd.NaT, pd.NaT

    # Remove timezone info if present
    if hasattr(shift_start_time, 'tz') and shift_start_time.tz is not None:
        shift_start_time = shift_start_time.tz_localize(None)

    shift_start_time = pd.to_datetime(shift_start_time)

    if 6 <= shift_start_time.hour < 18:
        shift_start = shift_start_time.replace(hour=6, minute=0, second=0, microsecond=0)
        shift_end = shift_start_time.replace(hour=18, minute=0, second=0, microsecond=0)
    else:
        if shift_start_time.hour < 6:
            shift_start = (shift_start_time - timedelta(days=1)).replace(hour=18, minute=0, second=0, microsecond=0)
        else:
            shift_start = shift_start_time.replace(hour=18, minute=0, second=0, microsecond=0)
        shift_end = shift_start + timedelta(hours=12)

    return shift_start, shift_end

# Example:
now = pd.Timestamp.now()
shift_start, shift_end = get_shift_start_end(now-timedelta(hours=12))
print("Shift start:", shift_start)
print("Shift end:", shift_end)

conn = sqlite3.connect("data.db")
cursor = conn.cursor()

shift_start_str = shift_start.isoformat()
shift_end_str = shift_end.isoformat()

cursor.execute(f"""
SELECT ownergroup, main_equipment, wonum, description, changedate
FROM WO_raw_data
WHERE changedate > ? AND changedate < ?
ORDER BY changedate DESC
""", (shift_start_str, shift_end_str))

rows = cursor.fetchall()
conn.close()

from itertools import groupby

def build_html_table(rows):
    if not rows:
        return "<p>No records found.</p>"

    headers = ['Trade', 'Equipment', 'WO No.', 'Description']
    table_html = "<table border='1' cellspacing='0' cellpadding='4' style='border-collapse: collapse;'>"
    
    # Build header row with centered style for first three headers
    table_html += "<thead><tr>"
    for h in headers:
        if h in ['Trade', 'Equipment', 'WO No.']:
            table_html += f"<th style='text-align:center'>{h}</th>"
        else:
            table_html += f"<th>{h}</th>"
    table_html += "</tr></thead>"

    table_html += "<tbody>"

    # Sort rows by ownergroup and main_equipment
    rows = sorted(rows, key=lambda r: (r[0], r[1]))

    for ownergroup, group_rows in groupby(rows, key=lambda r: r[0]):
        group_rows = list(group_rows)
        ownergroup_rowspan = len(group_rows)

        main_equipment_groups = []
        for equipment, equip_rows in groupby(group_rows, key=lambda r: r[1]):
            equip_rows = list(equip_rows)
            main_equipment_groups.append((equipment, equip_rows))

        ownergroup_written = False

        for equipment, equip_rows in main_equipment_groups:
            equipment_rowspan = len(equip_rows)
            equipment_written = False

            for i, row in enumerate(equip_rows):
                wonum = row[2]
                description = row[3]

                table_html += "<tr>"

                if not ownergroup_written:
                    table_html += f"<td rowspan='{ownergroup_rowspan}' style='text-align:center'>{ownergroup}</td>"
                    ownergroup_written = True

                if not equipment_written:
                    table_html += f"<td rowspan='{equipment_rowspan}' style='text-align:center'>{equipment}</td>"
                    equipment_written = True

                table_html += f"<td style='text-align:center'>{wonum}</td>"
                table_html += f"<td>{description}</td>"

                table_html += "</tr>"

    table_html += "</tbody></table>"
    return table_html

def get_shift_letter(shift_start: datetime) -> str:
    # Reference date (January 1, 1900)
    ref_date = datetime(1900, 1, 1)
    
    # Calculate days since reference date
    days_since_ref_date_shift = (shift_start - ref_date).days
    days_since_ref_date_2024 = (datetime(2024, 1, 1) - ref_date).days

    # Shift pattern determination
    if 6 <= shift_start.hour < 18:
        # Day shifts (A or B)
        mod_value = (days_since_ref_date_shift - days_since_ref_date_2024 + 45292 + 18) % 6
        if mod_value < 3:
            return "A"
        else:
            return "C"
    else:
        # Night shifts (C or D)
        mod_value = (days_since_ref_date_shift - days_since_ref_date_2024 + 45292 - 12) % 6
        if mod_value < 3:
            return "B"
        else:
            return "D"
        
# Previous crew
crew_name = get_shift_letter(datetime.now() - timedelta(hours=12))

# Determine shift type based on 'Crew' column
shift = "Day" if crew_name in ['A', 'C'] else "Night"
        
# Sending email
outlook = win32.Dispatch('Outlook.Application')
mail = outlook.CreateItem(0)
# Set Email Subject
mail.Subject = f"EMS Work Orders Completion on {crew_name} CREW - ({shift})"

mail.To = ';'.join([
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
])


# Email Copy (CC)
mail.CC = ';'.join([
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
])

# Email Body (HTML format)
mail.BodyFormat = 2  # 2 is for HTML0

html_table = build_html_table(rows)

mail.HTMLBody = f"""
<p>Hello Ultium Facility,</p>
<p>Here are the work orders that were completed on {crew_name}-CREW ({shift})</p>
<p>Shift Start: {shift_start.strftime('%Y-%m-%d %H:%M:%S')}<br>Shift End: {shift_end.strftime('%Y-%m-%d %H:%M:%S')}</p>
<p>Completed {len(rows)} work orders:</p>

<p></p>
{html_table}
<p>Thank you,<br>Joshua Jang</p>
"""

mail.Display()  # Open email for review before sending
# mail.Send()
print("✅ Email draft successfully created.")