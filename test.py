# import requests
# import json
# import urllib3

# urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# # --- Configuration ---
# BASE_URL = "https://ems-lgensol.singlex.com/maximo"
# # OBJECT_STRUCTURE = "MXAPIWODETAIL" # has zwocomments(OP result comments)
# OBJECT_STRUCTURE = "ZLGAPIWO"
# API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

# # Make sure WHERE clause uses valid Maximo field names and values
# where_clause = (
#     'siteid="UTIL.GM" and wonum="GM15544851"'
# )

# params = {
#     "oslc.where": where_clause,
#     "oslc.select": "*,z_ex_measurement_dm{zremarks}",
#     "oslc.pageSize": "1",
#     "lean": "1"
# }

# headers = {
#     "Accept": "application/json",
#     "apikey": API_KEY
# }

# url = f"{BASE_URL}/oslc/os/{OBJECT_STRUCTURE}"

# # 🔍 Make the API call
# resp = requests.get(url, headers=headers, params=params, verify=False)
# resp.raise_for_status()  # Will raise 400 if query is malformed

# # ✅ Save the response
# with open("response.json", "w", encoding="utf-8") as f:
#     json.dump(resp.json(), f, ensure_ascii=False, indent=2)

# print("✅ Saved response to 'response.json'")


import requests
import urllib3
import json

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# --- Configuration ---
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_STRUCTURE = "Z_EX_MEASUREMENT_DM"
url = f"{BASE_URL}/oslc/os/{OBJECT_STRUCTURE}"

# --- WHERE clause ---
where_clause = 'siteid="UTIL.GM" and zpmnum="CHK00295934"'

# --- Parameters ---
params = {
    "oslc.where": where_clause,
    "lean": "1",
    "oslc.select": (
        "z_ex_measurementid,zobservation,zinspector,"
        "zinspector.displayname--zinspectorname,zmetertype,zmetertype_description,"
        "zassetnum,zasset.description--assetdesc,zpointnum,zpmnum,zremarks,zdetail,"
        "zmetername,zdomainid,zschedstart,zschedfinish,"
        "workorder.schedstart--woschedstart,workorder.schedfinish--woschedfinish,"
        "doclinks{weburl,urlname,doctype},"
        "rel.zv_pn_checkmst{pmuid}"
    )
}

# --- Headers ---
headers = {
    "Accept": "application/json",
    "apikey": API_KEY
}

# --- Make Request ---
resp = requests.get(url, headers=headers, params=params, verify=False)
resp.raise_for_status()

# --- Save full response ---
data = resp.json()
with open("full_measurement_details.json", "w", encoding="utf-8") as f:
    json.dump(data, f, indent=2, ensure_ascii=False)

# --- Extract zremarks ---
zremarks = None
members = data.get("member", [])
if members:
    zremarks = members[0].get("zremarks")

print("📝 zremarks:", zremarks)