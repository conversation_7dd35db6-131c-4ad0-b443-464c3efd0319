'''
This script will get all the list of object structures from the system.
'''

import requests
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "MXINTOBJECT"  # Try this, or check with your admin
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,            # or 'maxauth' if your system uses that header
    # "maxauth": API_KEY,
    "Content-Type": "application/json"
}

params = {
    "lean": "1",
    "oslc.select": "intobjectname,description,rel.maxintobjdetail{objectname,hierarchypath}",
    "oslc.pageSize": "1001",
    "oslc.where": 'usewith="INTEGRATION"'
}

response = requests.get(f"{BASE_URL}/oslc/os/{OBJECT_NAME}", headers=headers, params=params, verify=False)

if response.ok:
    data = response.json()
    print(f"Found {len(data.get('member', []))} object structures:")
    for member in data.get("member", []):
        print(f"{member.get('intobjectname')}: {member.get('description')}")
else:
    print(f"Failed with status {response.status_code}: {response.text}")
