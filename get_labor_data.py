import requests
import json
import sqlite3
import urllib3
import os

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# --- Configuration ---
BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_STRUCTURE = "ZLGAPIUSERINFO"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
DB_PATH = "data.db"
TABLE_NAME = "labor_users"

# --- WHERE Clause ---
where_clause = 'defsite="UTIL.GM"'

params = {
    "oslc.where": where_clause,
    "oslc.select": "person",
    "lean": "1"
}

headers = {
    "Accept": "application/json",
    "apikey": API_KEY
}

# --- Step 1: Fetch the data ---
try:
    response = requests.get(
        f"{BASE_URL}/oslc/os/{OBJECT_STRUCTURE}",
        headers=headers,
        params=params,
        verify=False
    )
    response.raise_for_status()
except requests.RequestException as e:
    print(f"❌ Request failed: {e}")
    exit(1)

print("✅ Request successful.")
results = response.json()
members = results.get("member", [])

# --- Step 2: Extract person details ---
records = []
for m in members:
    person_list = m.get("person", [])
    for p in person_list:
        records.append((
            p.get("displayname"),
            p.get("personid"),
            p.get("title")
        ))

print(f"🔢 Extracted {len(records)} records.")

# --- Step 3: Save to SQLite ---
if not os.path.exists(DB_PATH):
    open(DB_PATH, "w").close()

conn = sqlite3.connect(DB_PATH)
cur = conn.cursor()

cur.execute(f"DROP TABLE IF EXISTS {TABLE_NAME}")
cur.execute(f"""
    CREATE TABLE {TABLE_NAME} (
        displayname TEXT,
        personid TEXT PRIMARY KEY,
        title TEXT
    )
""")

cur.executemany(f"""
    INSERT INTO {TABLE_NAME} (displayname, personid, title)
    VALUES (?, ?, ?)
""", records)

conn.commit()
conn.close()

print(f"✅ Inserted {len(records)} rows into '{TABLE_NAME}' in '{DB_PATH}'.")
